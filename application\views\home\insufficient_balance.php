<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header bg-warning text-dark text-center">
                <h4 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Saldo Insuficiente
                </h4>
            </div>
            <div class="card-body text-center py-5">
                <i class="fas fa-coins fa-5x text-warning mb-4"></i>
                <h2 class="mb-3">Ops! Você não possui saldo suficiente</h2>
                <p class="lead text-muted mb-4">
                    Para jogar nossos games, você precisa ter saldo em sua conta.
                </p>
                
                <div class="row mb-4">
                    <div class="col-md-6 mb-3">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h5><i class="fas fa-user me-2"></i>Seu <PERSON>do <PERSON>ual</h5>
                                <h3 class="text-danger">R$ <?= number_format($user['balance'], 2, ',', '.') ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h5><i class="fas fa-gamepad me-2"></i>Saldo Mínimo</h5>
                                <h3 class="text-success">R$ 1,00</h3>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="alert alert-info">
                    <i class="fas fa-lightbulb me-2"></i>
                    <strong>Dica:</strong> Adicione pelo menos R$ 10,00 para ter uma boa experiência de jogo!
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                    <button class="btn btn-success btn-lg me-md-2" onclick="showAddBalanceModal()">
                        <i class="fas fa-plus-circle me-2"></i>
                        Adicionar Saldo
                    </button>
                    <a href="<?= base_url('api_purchase') ?>" class="btn btn-warning btn-lg">
                        <i class="fas fa-shopping-cart me-2"></i>
                        Comprar API
                    </a>
                </div>

                <hr class="my-4">

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <a href="<?= base_url() ?>" class="btn btn-outline-primary w-100">
                            <i class="fas fa-home me-2"></i>
                            Voltar ao Dashboard
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="<?= base_url('games') ?>" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-gamepad me-2"></i>
                            Ver Todos os Games
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <button class="btn btn-outline-info w-100" onclick="showHelpModal()">
                            <i class="fas fa-question-circle me-2"></i>
                            Precisa de Ajuda?
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- How to Add Balance Info -->
<div class="row justify-content-center mt-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Como Adicionar Saldo
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-credit-card me-2 text-success"></i>Método 1: Adicionar Saldo</h6>
                        <ol>
                            <li>Clique no botão "Adicionar Saldo"</li>
                            <li>Digite o valor desejado</li>
                            <li>Confirme a operação</li>
                            <li>O saldo será adicionado instantaneamente</li>
                        </ol>
                        <div class="alert alert-success">
                            <small><i class="fas fa-check me-1"></i>Rápido e fácil para testes</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-shopping-cart me-2 text-warning"></i>Método 2: Comprar API</h6>
                        <ol>
                            <li>Acesse a página de compra de API</li>
                            <li>Escolha um plano</li>
                            <li>Complete o processo de compra</li>
                            <li>Receba acesso completo à plataforma</li>
                        </ol>
                        <div class="alert alert-warning">
                            <small><i class="fas fa-star me-1"></i>Ideal para uso comercial</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Balance Add -->
<div class="row justify-content-center mt-4">
    <div class="col-lg-8">
        <div class="card border-success">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-zap me-2"></i>
                    Adição Rápida de Saldo
                </h5>
            </div>
            <div class="card-body">
                <p class="mb-3">Adicione saldo rapidamente com valores pré-definidos:</p>
                <div class="row">
                    <div class="col-6 col-md-3 mb-2">
                        <button class="btn btn-outline-success w-100" onclick="quickAddBalance(10)">
                            + R$ 10,00
                        </button>
                    </div>
                    <div class="col-6 col-md-3 mb-2">
                        <button class="btn btn-outline-success w-100" onclick="quickAddBalance(25)">
                            + R$ 25,00
                        </button>
                    </div>
                    <div class="col-6 col-md-3 mb-2">
                        <button class="btn btn-outline-success w-100" onclick="quickAddBalance(50)">
                            + R$ 50,00
                        </button>
                    </div>
                    <div class="col-6 col-md-3 mb-2">
                        <button class="btn btn-outline-success w-100" onclick="quickAddBalance(100)">
                            + R$ 100,00
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Help Modal -->
<div class="modal fade" id="helpModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-question-circle me-2"></i>
                    Precisa de Ajuda?
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>Perguntas Frequentes:</h6>
                
                <div class="accordion" id="faqAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                Por que preciso de saldo para jogar?
                            </button>
                        </h2>
                        <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                O saldo é necessário para simular apostas reais nos games. Isso garante uma experiência autêntica de jogo e permite testar todas as funcionalidades.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                O saldo adicionado é real?
                            </button>
                        </h2>
                        <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Não, este é um ambiente de demonstração. O saldo é virtual e serve apenas para testes. Para uso comercial, considere comprar um plano de API.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                Como funciona a compra de API?
                            </button>
                        </h2>
                        <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                A compra de API dá acesso completo à nossa plataforma para integração em seu próprio sistema. Você recebe credenciais exclusivas e suporte técnico.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                <a href="mailto:<EMAIL>" class="btn btn-primary">
                    <i class="fas fa-envelope me-1"></i>Contatar Suporte
                </a>
            </div>
        </div>
    </div>
</div>

<script>
    // Show Help Modal
    function showHelpModal() {
        const modal = new bootstrap.Modal(document.getElementById('helpModal'));
        modal.show();
    }

    // Quick Add Balance
    async function quickAddBalance(amount) {
        try {
            const formData = new FormData();
            formData.append('amount', amount);
            
            const response = await fetch('<?= base_url("add_balance") ?>', {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (data.status === 1) {
                // Show success message
                showAlert('success', `R$ ${amount.toFixed(2)} adicionados com sucesso!`);
                
                // Redirect to games after 2 seconds
                setTimeout(() => {
                    window.location.href = '<?= base_url("games") ?>';
                }, 2000);
            } else {
                showAlert('danger', data.msg);
            }
        } catch (error) {
            console.error('Error:', error);
            showAlert('danger', 'Erro ao adicionar saldo. Tente novamente.');
        }
    }

    // Show Alert Function
    function showAlert(type, message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const container = document.querySelector('.container');
        container.insertBefore(alertDiv, container.firstChild);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }
</script>

<style>
    .card {
        border: none;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .btn-lg {
        padding: 12px 30px;
        font-size: 1.1rem;
    }

    .alert {
        border: none;
        border-radius: 10px;
    }

    .accordion-button:not(.collapsed) {
        background-color: var(--bs-primary);
        color: white;
    }

    .accordion-button:focus {
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }

    @media (max-width: 768px) {
        .d-grid.gap-2.d-md-flex {
            gap: 0.5rem !important;
        }
        
        .btn-lg {
            padding: 10px 20px;
            font-size: 1rem;
        }
    }
</style>
