-- Frontend Users Setup
-- Create frontend users table and test user

-- Create frontend_users table
CREATE TABLE IF NOT EXISTS `frontend_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `balance` decimal(10,2) DEFAULT 0.00,
  `status` tinyint(1) DEFAULT 1,
  `is_test_user` tinyint(1) DEFAULT 0,
  `last_login` timestamp NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`),
  KEY `status` (`status`),
  KEY `is_test_user` (`is_test_user`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Create user_transactions table for balance history
CREATE TABLE IF NOT EXISTS `user_transactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `type` enum('credit','debit') NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `type` (`type`),
  KEY `created_at` (`created_at`),
  FOREIGN KEY (`user_id`) REFERENCES `frontend_users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Insert test user with R$ 200.00 balance
-- Password: 123456 (hashed with PHP password_hash)
INSERT INTO `frontend_users` (`name`, `email`, `password`, `balance`, `status`, `is_test_user`, `created_at`) 
VALUES (
  'Usuário Teste',
  '<EMAIL>',
  '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: 123456
  200.00,
  1,
  1,
  NOW()
) ON DUPLICATE KEY UPDATE 
  `balance` = 200.00,
  `status` = 1,
  `is_test_user` = 1;

-- Insert initial transaction for test user
INSERT INTO `user_transactions` (`user_id`, `type`, `amount`, `description`, `created_at`)
SELECT 
  `id`,
  'credit',
  200.00,
  'Saldo inicial para testes',
  NOW()
FROM `frontend_users` 
WHERE `email` = '<EMAIL>'
AND NOT EXISTS (
  SELECT 1 FROM `user_transactions` ut 
  WHERE ut.user_id = `frontend_users`.id 
  AND ut.description = 'Saldo inicial para testes'
);

-- Create additional test users for demonstration
INSERT INTO `frontend_users` (`name`, `email`, `password`, `balance`, `status`, `is_test_user`, `created_at`) 
VALUES 
  (
    'Demo User 1',
    '<EMAIL>',
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: 123456
    50.00,
    1,
    1,
    NOW()
  ),
  (
    'Demo User 2',
    '<EMAIL>',
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: 123456
    100.00,
    1,
    1,
    NOW()
  )
ON DUPLICATE KEY UPDATE 
  `status` = 1,
  `is_test_user` = 1;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS `idx_frontend_users_email_status` ON `frontend_users` (`email`, `status`);
CREATE INDEX IF NOT EXISTS `idx_frontend_users_balance` ON `frontend_users` (`balance`);
CREATE INDEX IF NOT EXISTS `idx_user_transactions_user_type` ON `user_transactions` (`user_id`, `type`);

-- Show created users
SELECT 
  id,
  name,
  email,
  balance,
  status,
  is_test_user,
  created_at
FROM frontend_users 
WHERE is_test_user = 1
ORDER BY created_at DESC;
