<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    Dashboard - Bem-vindo, <?= $user['name'] ?>!
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- Balance Card -->
                    <div class="col-md-4 mb-4">
                        <div class="card bg-gradient-success text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-coins fa-3x mb-3"></i>
                                <h5>Saldo Atual</h5>
                                <h2>R$ <?= number_format($user['balance'], 2, ',', '.') ?></h2>
                                <button class="btn btn-light btn-sm mt-2" onclick="showAddBalanceModal()">
                                    <i class="fas fa-plus me-1"></i><PERSON><PERSON><PERSON><PERSON>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Games Card -->
                    <div class="col-md-4 mb-4">
                        <div class="card bg-gradient-primary text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-gamepad fa-3x mb-3"></i>
                                <h5>Games Disponíveis</h5>
                                <h2>50+</h2>
                                <a href="<?= base_url('games') ?>" class="btn btn-light btn-sm mt-2">
                                    <i class="fas fa-play me-1"></i>Jogar Agora
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- API Card -->
                    <div class="col-md-4 mb-4">
                        <div class="card bg-gradient-warning text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                                <h5>Créditos API</h5>
                                <h2>Ilimitado</h2>
                                <a href="<?= base_url('api_purchase') ?>" class="btn btn-light btn-sm mt-2">
                                    <i class="fas fa-credit-card me-1"></i>Comprar Mais
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    Ações Rápidas
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="<?= base_url('games') ?>" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center" style="min-height: 120px;">
                            <i class="fas fa-gamepad fa-2x mb-2"></i>
                            <span>Ver Todos os Games</span>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button onclick="showAddBalanceModal()" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center" style="min-height: 120px;">
                            <i class="fas fa-plus-circle fa-2x mb-2"></i>
                            <span>Adicionar Saldo</span>
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?= base_url('api_purchase') ?>" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center" style="min-height: 120px;">
                            <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                            <span>Comprar API</span>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button onclick="showHelpModal()" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center" style="min-height: 120px;">
                            <i class="fas fa-question-circle fa-2x mb-2"></i>
                            <span>Ajuda</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Popular Games -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-fire me-2"></i>
                    Games Populares
                </h5>
            </div>
            <div class="card-body">
                <div class="row" id="popularGames">
                    <!-- Games will be loaded here -->
                    <div class="col-12 text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Carregando...</span>
                        </div>
                        <p class="mt-2">Carregando games populares...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Help Modal -->
<div class="modal fade" id="helpModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-question-circle me-2"></i>
                    Ajuda - Como Usar a Plataforma
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="accordion" id="helpAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#help1">
                                <i class="fas fa-coins me-2"></i>Como adicionar saldo?
                            </button>
                        </h2>
                        <div id="help1" class="accordion-collapse collapse show" data-bs-parent="#helpAccordion">
                            <div class="accordion-body">
                                <p>Para adicionar saldo à sua conta:</p>
                                <ol>
                                    <li>Clique no botão "Adicionar Saldo" no seu perfil</li>
                                    <li>Digite o valor desejado</li>
                                    <li>Confirme a operação</li>
                                </ol>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Usuário de Teste:</strong> Você já possui R$ 200,00 para testar os games!
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#help2">
                                <i class="fas fa-gamepad me-2"></i>Como jogar os games?
                            </button>
                        </h2>
                        <div id="help2" class="accordion-collapse collapse" data-bs-parent="#helpAccordion">
                            <div class="accordion-body">
                                <p>Para jogar os games:</p>
                                <ol>
                                    <li>Acesse a seção "Games"</li>
                                    <li>Escolha o game desejado</li>
                                    <li>Clique em "Jogar Agora"</li>
                                    <li>O game abrirá em uma nova janela</li>
                                </ol>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>Importante:</strong> Você precisa ter saldo para jogar!
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#help3">
                                <i class="fas fa-shopping-cart me-2"></i>O que é a API?
                            </button>
                        </h2>
                        <div id="help3" class="accordion-collapse collapse" data-bs-parent="#helpAccordion">
                            <div class="accordion-body">
                                <p>A API permite integrar nossos games em sua própria plataforma:</p>
                                <ul>
                                    <li>Acesso completo aos games</li>
                                    <li>Documentação técnica</li>
                                    <li>Suporte técnico</li>
                                    <li>Personalização avançada</li>
                                </ul>
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle me-2"></i>
                                    <strong>Perfeito para:</strong> Desenvolvedores e empresas que querem integrar games em suas plataformas.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                <a href="mailto:<EMAIL>" class="btn btn-primary">
                    <i class="fas fa-envelope me-1"></i>Contatar Suporte
                </a>
            </div>
        </div>
    </div>
</div>

<script>
    // Show Help Modal
    function showHelpModal() {
        const modal = new bootstrap.Modal(document.getElementById('helpModal'));
        modal.show();
    }

    // Load Popular Games
    document.addEventListener('DOMContentLoaded', function() {
        loadPopularGames();
    });

    async function loadPopularGames() {
        try {
            // Simulate API call - replace with actual API endpoint
            const popularGames = [
                { code: 'vs20sugarrush', name: 'Sugar Rush', image: '🍭' },
                { code: 'vs20olympgate', name: 'Gates of Olympus', image: '⚡' },
                { code: 'vs20doghouse', name: 'The Dog House', image: '🐕' },
                { code: 'vs25wolfgold', name: 'Wolf Gold', image: '🐺' },
                { code: 'vs20starlight', name: 'Starlight Princess', image: '⭐' },
                { code: 'vs20bonzgold', name: 'Bonanza Gold', image: '💎' }
            ];

            const gamesContainer = document.getElementById('popularGames');
            gamesContainer.innerHTML = '';

            popularGames.forEach(game => {
                const gameCard = `
                    <div class="col-md-4 col-lg-2 mb-3">
                        <div class="card game-card h-100" style="cursor: pointer;" onclick="launchGame('${game.code}', '${game.name}')">
                            <div class="card-body text-center">
                                <div style="font-size: 3rem; margin-bottom: 1rem;">${game.image}</div>
                                <h6 class="card-title">${game.name}</h6>
                                <button class="btn btn-primary btn-sm">
                                    <i class="fas fa-play me-1"></i>Jogar
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                gamesContainer.innerHTML += gameCard;
            });

        } catch (error) {
            console.error('Error loading popular games:', error);
            document.getElementById('popularGames').innerHTML = `
                <div class="col-12 text-center">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Erro ao carregar games populares. <a href="<?= base_url('games') ?>">Ver todos os games</a>
                    </div>
                </div>
            `;
        }
    }
</script>

<style>
    .game-card {
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .game-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
    }

    .bg-gradient-success {
        background: linear-gradient(135deg, #10b981, #059669) !important;
    }

    .bg-gradient-primary {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
    }

    .bg-gradient-warning {
        background: linear-gradient(135deg, #f59e0b, #d97706) !important;
    }
</style>
