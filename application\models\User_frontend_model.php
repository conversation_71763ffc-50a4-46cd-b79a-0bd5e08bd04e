<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class User_frontend_model extends CI_Model {

    public function __construct() {
        parent::__construct();
        $this->load->database();
    }

    public function create($data) {
        $this->db->insert('frontend_users', $data);
        return $this->db->insert_id();
    }

    public function get_by_id($id) {
        $this->db->where('id', $id);
        $query = $this->db->get('frontend_users');
        return $query->row_array();
    }

    public function get_by_email($email) {
        $this->db->where('email', $email);
        $query = $this->db->get('frontend_users');
        return $query->row_array();
    }

    public function verify_login($email, $password) {
        $user = $this->get_by_email($email);
        
        if ($user && password_verify($password, $user['password']) && $user['status'] == 1) {
            return $user;
        }
        
        return false;
    }

    public function update_last_login($id) {
        $data = [
            'last_login' => date('Y-m-d H:i:s')
        ];
        
        $this->db->where('id', $id);
        return $this->db->update('frontend_users', $data);
    }

    public function add_balance($user_id, $amount) {
        $this->db->set('balance', 'balance + ' . floatval($amount), FALSE);
        $this->db->where('id', $user_id);
        return $this->db->update('frontend_users');
    }

    public function subtract_balance($user_id, $amount) {
        $this->db->set('balance', 'balance - ' . floatval($amount), FALSE);
        $this->db->where('id', $user_id);
        $this->db->where('balance >=', $amount); // Prevent negative balance
        return $this->db->update('frontend_users');
    }

    public function get_balance($user_id) {
        $this->db->select('balance');
        $this->db->where('id', $user_id);
        $query = $this->db->get('frontend_users');
        $result = $query->row_array();
        return $result ? $result['balance'] : 0;
    }

    public function update_balance($user_id, $new_balance) {
        $data = [
            'balance' => $new_balance,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        $this->db->where('id', $user_id);
        return $this->db->update('frontend_users', $data);
    }

    public function get_all_users($limit = 50, $offset = 0) {
        $this->db->select('id, name, email, balance, status, created_at, last_login');
        $this->db->order_by('created_at', 'DESC');
        $this->db->limit($limit, $offset);
        $query = $this->db->get('frontend_users');
        return $query->result_array();
    }

    public function count_users() {
        return $this->db->count_all('frontend_users');
    }

    public function update_status($user_id, $status) {
        $data = [
            'status' => $status,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        $this->db->where('id', $user_id);
        return $this->db->update('frontend_users', $data);
    }

    public function delete_user($user_id) {
        $this->db->where('id', $user_id);
        return $this->db->delete('frontend_users');
    }

    public function create_test_user() {
        // Check if test user already exists
        $existing = $this->get_by_email('<EMAIL>');
        if ($existing) {
            return $existing['id'];
        }

        $data = [
            'name' => 'Usuário Teste',
            'email' => '<EMAIL>',
            'password' => password_hash('123456', PASSWORD_DEFAULT),
            'balance' => 200.00,
            'status' => 1,
            'created_at' => date('Y-m-d H:i:s'),
            'is_test_user' => 1
        ];

        return $this->create($data);
    }

    public function get_user_transactions($user_id, $limit = 20) {
        $this->db->where('user_id', $user_id);
        $this->db->order_by('created_at', 'DESC');
        $this->db->limit($limit);
        $query = $this->db->get('user_transactions');
        return $query->result_array();
    }

    public function add_transaction($user_id, $type, $amount, $description = '') {
        $data = [
            'user_id' => $user_id,
            'type' => $type, // 'credit' or 'debit'
            'amount' => $amount,
            'description' => $description,
            'created_at' => date('Y-m-d H:i:s')
        ];

        return $this->db->insert('user_transactions', $data);
    }
}
