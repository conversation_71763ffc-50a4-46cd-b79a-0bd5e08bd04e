<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-shopping-cart me-2"></i>
                    Comprar Créditos API
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-8">
                        <h5>Planos Disponíveis</h5>
                        <p class="text-muted">Escolha o plano que melhor se adequa às suas necessidades</p>
                        
                        <div class="row">
                            <!-- Basic Plan -->
                            <div class="col-md-4 mb-4">
                                <div class="card plan-card h-100">
                                    <div class="card-header text-center bg-light">
                                        <h5 class="mb-0">Básico</h5>
                                        <small class="text-muted">Para testes</small>
                                    </div>
                                    <div class="card-body text-center">
                                        <div class="price mb-3">
                                            <span class="h2">R$ 99</span>
                                            <small class="text-muted">/mês</small>
                                        </div>
                                        <ul class="list-unstyled">
                                            <li><i class="fas fa-check text-success me-2"></i>1.000 chamadas/mês</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Suporte por email</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Documentação completa</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Ambiente de teste</li>
                                        </ul>
                                        <button class="btn btn-outline-primary w-100" onclick="selectPlan('basic', 99)">
                                            Escolher Plano
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Professional Plan -->
                            <div class="col-md-4 mb-4">
                                <div class="card plan-card h-100 border-primary">
                                    <div class="card-header text-center bg-primary text-white">
                                        <h5 class="mb-0">Profissional</h5>
                                        <small>Mais Popular</small>
                                    </div>
                                    <div class="card-body text-center">
                                        <div class="price mb-3">
                                            <span class="h2">R$ 299</span>
                                            <small class="text-muted">/mês</small>
                                        </div>
                                        <ul class="list-unstyled">
                                            <li><i class="fas fa-check text-success me-2"></i>10.000 chamadas/mês</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Suporte prioritário</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Webhook personalizado</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Relatórios avançados</li>
                                            <li><i class="fas fa-check text-success me-2"></i>SSL incluso</li>
                                        </ul>
                                        <button class="btn btn-primary w-100" onclick="selectPlan('professional', 299)">
                                            Escolher Plano
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Enterprise Plan -->
                            <div class="col-md-4 mb-4">
                                <div class="card plan-card h-100">
                                    <div class="card-header text-center bg-warning text-dark">
                                        <h5 class="mb-0">Empresarial</h5>
                                        <small>Para grandes volumes</small>
                                    </div>
                                    <div class="card-body text-center">
                                        <div class="price mb-3">
                                            <span class="h2">R$ 799</span>
                                            <small class="text-muted">/mês</small>
                                        </div>
                                        <ul class="list-unstyled">
                                            <li><i class="fas fa-check text-success me-2"></i>Chamadas ilimitadas</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Suporte 24/7</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Customização completa</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Servidor dedicado</li>
                                            <li><i class="fas fa-check text-success me-2"></i>SLA garantido</li>
                                        </ul>
                                        <button class="btn btn-warning w-100" onclick="selectPlan('enterprise', 799)">
                                            Escolher Plano
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card bg-light">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Informações da API
                                </h5>
                            </div>
                            <div class="card-body">
                                <h6>O que você recebe:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-key text-primary me-2"></i>Chave de API exclusiva</li>
                                    <li><i class="fas fa-book text-primary me-2"></i>Documentação completa</li>
                                    <li><i class="fas fa-code text-primary me-2"></i>Exemplos de código</li>
                                    <li><i class="fas fa-shield-alt text-primary me-2"></i>Conexão segura (HTTPS)</li>
                                    <li><i class="fas fa-chart-line text-primary me-2"></i>Dashboard de monitoramento</li>
                                </ul>

                                <hr>

                                <h6>Métodos disponíveis:</h6>
                                <ul class="list-unstyled small">
                                    <li><code>game_list</code> - Listar games</li>
                                    <li><code>game_launch</code> - Lançar game</li>
                                    <li><code>user_create</code> - Criar usuário</li>
                                    <li><code>user_deposit</code> - Depositar</li>
                                    <li><code>user_withdraw</code> - Sacar</li>
                                    <li><code>money_info</code> - Consultar saldo</li>
                                </ul>

                                <hr>

                                <div class="alert alert-info">
                                    <i class="fas fa-lightbulb me-2"></i>
                                    <strong>Dica:</strong> Comece com o plano Básico para testar a integração!
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Purchase Modal -->
<div class="modal fade" id="purchaseModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-credit-card me-2"></i>
                    Finalizar Compra
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="selectedPlanInfo" class="alert alert-primary">
                    <!-- Plan info will be inserted here -->
                </div>

                <form id="purchaseForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="cardName" class="form-label">Nome no Cartão</label>
                            <input type="text" class="form-control" id="cardName" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="cardNumber" class="form-label">Número do Cartão</label>
                            <input type="text" class="form-control" id="cardNumber" placeholder="1234 5678 9012 3456" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="cardExpiry" class="form-label">Validade</label>
                            <input type="text" class="form-control" id="cardExpiry" placeholder="MM/AA" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="cardCvv" class="form-label">CVV</label>
                            <input type="text" class="form-control" id="cardCvv" placeholder="123" required>
                        </div>
                    </div>
                    
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Modo Demo:</strong> Esta é uma simulação. Nenhum pagamento real será processado.
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="processPurchase()">
                    <i class="fas fa-credit-card me-1"></i>Processar Pagamento
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Success Modal -->
<div class="modal fade" id="successModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle me-2"></i>
                    Compra Realizada com Sucesso!
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                <h4>Parabéns!</h4>
                <p>Sua compra foi processada com sucesso. Você receberá suas credenciais de API por email em alguns minutos.</p>
                
                <div class="alert alert-info">
                    <h6><i class="fas fa-key me-2"></i>Suas Credenciais (Demo)</h6>
                    <p><strong>Agent Code:</strong> <code id="demoAgentCode"></code></p>
                    <p><strong>API Token:</strong> <code id="demoApiToken"></code></p>
                    <small class="text-muted">Guarde essas informações em local seguro</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                <a href="mailto:<EMAIL>" class="btn btn-primary">
                    <i class="fas fa-envelope me-1"></i>Contatar Suporte
                </a>
            </div>
        </div>
    </div>
</div>

<script>
    let selectedPlan = null;
    let selectedPrice = 0;

    function selectPlan(planName, price) {
        selectedPlan = planName;
        selectedPrice = price;
        
        const planNames = {
            'basic': 'Básico',
            'professional': 'Profissional', 
            'enterprise': 'Empresarial'
        };
        
        const planInfo = document.getElementById('selectedPlanInfo');
        planInfo.innerHTML = `
            <h6><i class="fas fa-package me-2"></i>Plano Selecionado: ${planNames[planName]}</h6>
            <p class="mb-0"><strong>Valor:</strong> R$ ${price.toFixed(2)}/mês</p>
        `;
        
        const modal = new bootstrap.Modal(document.getElementById('purchaseModal'));
        modal.show();
    }

    function processPurchase() {
        // Simulate payment processing
        const submitBtn = event.target;
        const originalText = submitBtn.innerHTML;
        
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Processando...';
        submitBtn.disabled = true;
        
        setTimeout(() => {
            // Hide purchase modal
            const purchaseModal = bootstrap.Modal.getInstance(document.getElementById('purchaseModal'));
            purchaseModal.hide();
            
            // Generate demo credentials
            const agentCode = 'demo_' + Math.random().toString(36).substr(2, 8);
            const apiToken = generateUUID();
            
            document.getElementById('demoAgentCode').textContent = agentCode;
            document.getElementById('demoApiToken').textContent = apiToken;
            
            // Show success modal
            const successModal = new bootstrap.Modal(document.getElementById('successModal'));
            successModal.show();
            
            // Reset button
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
            
        }, 2000);
    }

    function generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }

    // Format card number input
    document.getElementById('cardNumber')?.addEventListener('input', function(e) {
        let value = e.target.value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
        let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
        e.target.value = formattedValue;
    });

    // Format expiry date input
    document.getElementById('cardExpiry')?.addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length >= 2) {
            value = value.substring(0, 2) + '/' + value.substring(2, 4);
        }
        e.target.value = value;
    });

    // Format CVV input
    document.getElementById('cardCvv')?.addEventListener('input', function(e) {
        e.target.value = e.target.value.replace(/\D/g, '').substring(0, 3);
    });
</script>

<style>
    .plan-card {
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }

    .plan-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }

    .plan-card.border-primary {
        border-color: #0d6efd !important;
    }

    .price {
        font-weight: bold;
        color: #1f2937;
    }

    .list-unstyled li {
        padding: 0.25rem 0;
    }

    code {
        background: #f8f9fa;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 0.875rem;
    }

    .alert-info {
        border-left: 4px solid #0dcaf0;
    }

    @media (max-width: 768px) {
        .plan-card {
            margin-bottom: 1rem;
        }
    }
</style>
