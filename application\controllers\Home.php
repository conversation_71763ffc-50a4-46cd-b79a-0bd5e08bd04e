<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Home extends CI_Controller {

    public function __construct() {
        parent::__construct();
        $this->load->model('User_frontend_model');
        $this->load->library('session');
        $this->load->helper(['url', 'form']);
        $this->load->library('form_validation');
    }

    public function index() {
        // Check if user is logged in
        if (!$this->session->userdata('user_logged_in')) {
            redirect('login');
        }
        
        $data['title'] = 'Plataforma de Games';
        $data['user'] = $this->User_frontend_model->get_by_id($this->session->userdata('user_id'));
        
        $this->load->view('home/header', $data);
        $this->load->view('home/dashboard', $data);
        $this->load->view('home/footer');
    }

    public function login() {
        // If already logged in, redirect to dashboard
        if ($this->session->userdata('user_logged_in')) {
            redirect('/');
        }

        if ($this->input->method() == 'post') {
            $this->form_validation->set_rules('email', 'Email', 'required|valid_email');
            $this->form_validation->set_rules('password', 'Senha', 'required');

            if ($this->form_validation->run()) {
                $email = $this->input->post('email');
                $password = $this->input->post('password');

                $user = $this->User_frontend_model->verify_login($email, $password);

                if ($user) {
                    $this->session->set_userdata([
                        'user_logged_in' => TRUE,
                        'user_id' => $user['id'],
                        'user_email' => $user['email'],
                        'user_name' => $user['name'],
                        'user_balance' => $user['balance']
                    ]);

                    $this->User_frontend_model->update_last_login($user['id']);
                    redirect('/');
                } else {
                    $this->session->set_flashdata('error', 'Email ou senha inválidos');
                }
            }
        }

        $data['title'] = 'Login - Plataforma de Games';
        $this->load->view('home/login', $data);
    }

    public function register() {
        // If already logged in, redirect to dashboard
        if ($this->session->userdata('user_logged_in')) {
            redirect('/');
        }

        if ($this->input->method() == 'post') {
            $this->form_validation->set_rules('name', 'Nome', 'required|min_length[3]');
            $this->form_validation->set_rules('email', 'Email', 'required|valid_email|is_unique[frontend_users.email]');
            $this->form_validation->set_rules('password', 'Senha', 'required|min_length[6]');
            $this->form_validation->set_rules('confirm_password', 'Confirmar Senha', 'required|matches[password]');

            if ($this->form_validation->run()) {
                $data = [
                    'name' => $this->input->post('name'),
                    'email' => $this->input->post('email'),
                    'password' => password_hash($this->input->post('password'), PASSWORD_DEFAULT),
                    'balance' => 0.00,
                    'status' => 1,
                    'created_at' => date('Y-m-d H:i:s')
                ];

                $user_id = $this->User_frontend_model->create($data);

                if ($user_id) {
                    $this->session->set_flashdata('success', 'Conta criada com sucesso! Faça login para continuar.');
                    redirect('login');
                } else {
                    $this->session->set_flashdata('error', 'Erro ao criar conta. Tente novamente.');
                }
            }
        }

        $data['title'] = 'Registro - Plataforma de Games';
        $this->load->view('home/register', $data);
    }

    public function logout() {
        $this->session->unset_userdata(['user_logged_in', 'user_id', 'user_email', 'user_name', 'user_balance']);
        $this->session->set_flashdata('success', 'Logout realizado com sucesso');
        redirect('login');
    }

    public function games() {
        // Check if user is logged in
        if (!$this->session->userdata('user_logged_in')) {
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode(['status' => 0, 'msg' => 'Acesso negado']));
            return;
        }

        $data['title'] = 'Games Disponíveis';
        $data['user'] = $this->User_frontend_model->get_by_id($this->session->userdata('user_id'));
        
        $this->load->view('home/header', $data);
        $this->load->view('home/games', $data);
        $this->load->view('home/footer');
    }

    public function play($game_code = null) {
        // Check if user is logged in
        if (!$this->session->userdata('user_logged_in')) {
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode(['status' => 0, 'msg' => 'Acesso negado']));
            return;
        }

        if (!$game_code) {
            show_404();
        }

        $user = $this->User_frontend_model->get_by_id($this->session->userdata('user_id'));
        
        // Check if user has balance
        if ($user['balance'] <= 0) {
            $data['title'] = 'Saldo Insuficiente';
            $data['user'] = $user;
            $this->load->view('home/header', $data);
            $this->load->view('home/insufficient_balance', $data);
            $this->load->view('home/footer');
            return;
        }

        // Load game
        $data['game'] = $game_code;
        $data['user'] = 'test_user_' . $this->session->userdata('user_id');
        $data['lang'] = 'pt';
        $data['cur'] = 'R$';
        
        $this->load->view('game', $data);
    }

    public function api_purchase() {
        // Check if user is logged in
        if (!$this->session->userdata('user_logged_in')) {
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode(['status' => 0, 'msg' => 'Acesso negado']));
            return;
        }

        $data['title'] = 'Comprar Créditos API';
        $data['user'] = $this->User_frontend_model->get_by_id($this->session->userdata('user_id'));
        
        $this->load->view('home/header', $data);
        $this->load->view('home/api_purchase', $data);
        $this->load->view('home/footer');
    }

    public function add_balance() {
        // Check if user is logged in
        if (!$this->session->userdata('user_logged_in')) {
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode(['status' => 0, 'msg' => 'Acesso negado']));
            return;
        }

        if ($this->input->method() == 'post') {
            $amount = floatval($this->input->post('amount'));
            $user_id = $this->session->userdata('user_id');
            
            if ($amount > 0) {
                $this->User_frontend_model->add_balance($user_id, $amount);
                
                // Update session balance
                $user = $this->User_frontend_model->get_by_id($user_id);
                $this->session->set_userdata('user_balance', $user['balance']);
                
                $this->output
                    ->set_content_type('application/json')
                    ->set_output(json_encode(['status' => 1, 'msg' => 'Saldo adicionado com sucesso', 'new_balance' => $user['balance']]));
            } else {
                $this->output
                    ->set_content_type('application/json')
                    ->set_output(json_encode(['status' => 0, 'msg' => 'Valor inválido']));
            }
        }
    }

    // Protected route example - returns JSON error if not logged in
    public function protected_api() {
        if (!$this->session->userdata('user_logged_in')) {
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode(['status' => 0, 'msg' => 'Acesso negado']));
            return;
        }

        // Your protected API logic here
        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode(['status' => 1, 'msg' => 'Acesso autorizado', 'data' => 'Protected content']));
    }
}
