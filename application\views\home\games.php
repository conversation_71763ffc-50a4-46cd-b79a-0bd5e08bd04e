<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="fas fa-gamepad me-2"></i>
                    Games Disponíveis
                </h4>
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <select class="form-select form-select-sm" id="providerFilter">
                            <option value="">Todos os Provedores</option>
                            <option value="PRAGMATICPLAY">Pragmatic Play</option>
                        </select>
                    </div>
                    <div class="input-group input-group-sm" style="width: 250px;">
                        <input type="text" class="form-control" id="searchGames" placeholder="Buscar games...">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Loading State -->
                <div id="loadingGames" class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Carregando...</span>
                    </div>
                    <p class="mt-3">Carregando games...</p>
                </div>

                <!-- Games Grid -->
                <div id="gamesGrid" class="row" style="display: none;">
                    <!-- Games will be loaded here -->
                </div>

                <!-- No Games Found -->
                <div id="noGamesFound" class="text-center py-5" style="display: none;">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h5>Nenhum game encontrado</h5>
                    <p class="text-muted">Tente ajustar os filtros de busca</p>
                </div>

                <!-- Error State -->
                <div id="errorLoading" class="text-center py-5" style="display: none;">
                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                    <h5>Erro ao carregar games</h5>
                    <p class="text-muted">Verifique sua conexão e tente novamente</p>
                    <button class="btn btn-primary" onclick="loadGames()">
                        <i class="fas fa-redo me-1"></i>Tentar Novamente
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Game Stats -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-gamepad fa-2x text-primary mb-2"></i>
                <h5 id="totalGames">0</h5>
                <small class="text-muted">Total de Games</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-building fa-2x text-success mb-2"></i>
                <h5 id="totalProviders">1</h5>
                <small class="text-muted">Provedores</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-play fa-2x text-warning mb-2"></i>
                <h5 id="gamesPlayed">0</h5>
                <small class="text-muted">Games Jogados</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-coins fa-2x text-info mb-2"></i>
                <h5>R$ <?= number_format($user['balance'], 2, ',', '.') ?></h5>
                <small class="text-muted">Seu Saldo</small>
            </div>
        </div>
    </div>
</div>

<script>
    const API_BASE = window.location.origin;
    const AGENT_CODE = 'admin';
    const AGENT_TOKEN = '5f2dbdcb-a59d-42f8-9815-cb34a9723cd9';
    let allGames = [];
    let filteredGames = [];

    // Load games on page load
    document.addEventListener('DOMContentLoaded', function() {
        loadGames();
        setupEventListeners();
    });

    // Setup event listeners
    function setupEventListeners() {
        // Search functionality
        document.getElementById('searchGames').addEventListener('input', function() {
            filterGames();
        });

        // Provider filter
        document.getElementById('providerFilter').addEventListener('change', function() {
            filterGames();
        });
    }

    // Load games from API
    async function loadGames() {
        try {
            showLoadingState();

            const response = await fetch(`${API_BASE}/api`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    method: 'game_list',
                    agent_code: AGENT_CODE,
                    agent_token: AGENT_TOKEN,
                    provider_code: 'PRAGMATICPLAY'
                })
            });

            const data = await response.json();

            if (data.status === 1 && data.games) {
                allGames = data.games;
                filteredGames = [...allGames];
                displayGames(filteredGames);
                updateStats();
            } else {
                showErrorState();
                console.error('API Error:', data.msg);
            }
        } catch (error) {
            console.error('Error loading games:', error);
            showErrorState();
        }
    }

    // Display games in grid
    function displayGames(games) {
        const gamesGrid = document.getElementById('gamesGrid');
        const loadingDiv = document.getElementById('loadingGames');
        const noGamesDiv = document.getElementById('noGamesFound');
        const errorDiv = document.getElementById('errorLoading');

        // Hide all states
        loadingDiv.style.display = 'none';
        noGamesDiv.style.display = 'none';
        errorDiv.style.display = 'none';

        if (games.length === 0) {
            noGamesDiv.style.display = 'block';
            gamesGrid.style.display = 'none';
            return;
        }

        gamesGrid.style.display = 'flex';
        gamesGrid.innerHTML = '';

        games.forEach(game => {
            const gameCard = createGameCard(game);
            gamesGrid.appendChild(gameCard);
        });
    }

    // Create individual game card
    function createGameCard(game) {
        const col = document.createElement('div');
        col.className = 'col-lg-3 col-md-4 col-sm-6 mb-4';

        col.innerHTML = `
            <div class="card game-card h-100" onclick="launchGame('${game.game_code}', '${game.game_name}')">
                <div class="card-body text-center">
                    <div class="game-icon mb-3">
                        <i class="fas fa-gamepad fa-3x text-primary"></i>
                    </div>
                    <h6 class="card-title">${game.game_name}</h6>
                    <p class="card-text text-muted small">${game.game_code}</p>
                    <button class="btn btn-primary btn-sm w-100" onclick="event.stopPropagation(); launchGame('${game.game_code}', '${game.game_name}')">
                        <i class="fas fa-play me-1"></i>Jogar Agora
                    </button>
                </div>
                <div class="card-footer bg-transparent text-center">
                    <small class="text-muted">Pragmatic Play</small>
                </div>
            </div>
        `;

        return col;
    }

    // Filter games based on search and provider
    function filterGames() {
        const searchTerm = document.getElementById('searchGames').value.toLowerCase();
        const selectedProvider = document.getElementById('providerFilter').value;

        filteredGames = allGames.filter(game => {
            const matchesSearch = game.game_name.toLowerCase().includes(searchTerm) || 
                                game.game_code.toLowerCase().includes(searchTerm);
            const matchesProvider = !selectedProvider || game.provider === selectedProvider;
            
            return matchesSearch && matchesProvider;
        });

        displayGames(filteredGames);
        updateStats();
    }

    // Update statistics
    function updateStats() {
        document.getElementById('totalGames').textContent = filteredGames.length;
        // Games played would come from user data in real implementation
        document.getElementById('gamesPlayed').textContent = Math.floor(Math.random() * 10);
    }

    // Show loading state
    function showLoadingState() {
        document.getElementById('loadingGames').style.display = 'block';
        document.getElementById('gamesGrid').style.display = 'none';
        document.getElementById('noGamesFound').style.display = 'none';
        document.getElementById('errorLoading').style.display = 'none';
    }

    // Show error state
    function showErrorState() {
        document.getElementById('loadingGames').style.display = 'none';
        document.getElementById('gamesGrid').style.display = 'none';
        document.getElementById('noGamesFound').style.display = 'none';
        document.getElementById('errorLoading').style.display = 'block';
    }
</script>

<style>
    .game-card {
        transition: all 0.3s ease;
        cursor: pointer;
        border: none;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .game-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }

    .game-icon {
        background: linear-gradient(135deg, #f8fafc, #e2e8f0);
        border-radius: 50%;
        width: 80px;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
    }

    .card-title {
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 0.5rem;
    }

    .btn-primary {
        background: linear-gradient(135deg, #10b981, #059669);
        border: none;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 5px 15px rgba(16, 185, 129, 0.4);
    }

    .form-select:focus,
    .form-control:focus {
        border-color: #10b981;
        box-shadow: 0 0 0 0.2rem rgba(16, 185, 129, 0.25);
    }

    @media (max-width: 768px) {
        .card-header .d-flex {
            flex-direction: column;
            gap: 1rem;
        }
        
        .input-group {
            width: 100% !important;
        }
    }
</style>
